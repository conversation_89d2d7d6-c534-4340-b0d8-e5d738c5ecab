export const calculateNewLayout = (
  groupCount: number,
  rows: number,
  cols: number
) => {
  while (rows * cols < groupCount) {
    if (cols > rows) {
      rows++;
    } else {
      cols++;
    }
  }
  return { rows, cols };
};

export const getNullRowsOrCols = (
  items: (string | null)[],
  rows: number,
  cols: number
) => {
  const nullRows = [];
  const nullCols = [];

  // Check each row to see if all items are null
  for (let row = 0; row < rows; row++) {
    const start = row * cols;
    const end = start + cols;
    const rowItems = items.slice(start, end);

    if (rowItems.every((item) => item === null)) {
      nullRows.push(row);
    }
  }

  // Check each column to see if all items are null
  for (let col = 0; col < cols; col++) {
    let colAllNull = true;

    for (let row = 0; row < rows; row++) {
      const index = row * cols + col;

      if (items[index] !== null) {
        colAllNull = false;
        break;
      }
    }

    if (colAllNull) {
      nullCols.push(col);
    }
  }
  return {
    nullRow: nullRows.length > 0 ? nullRows[0] : undefined,
    nullCol: nullCols.length > 0 ? nullCols[0] : undefined,
  };
};

export const removeColOrRow = (
  items: (string | null)[],
  rows: number,
  cols: number,
  index: number,
  isRow = true
) => {
  const newItems = [...items];

  if (isRow) {
    // Remove all items from the specified row
    const start = index * cols;
    newItems.splice(start, cols);
  } else {
    // Remove all items from the specified column
    for (let row = rows - 1; row >= 0; row--) {
      const itemIndex = row * cols + index;
      newItems.splice(itemIndex, 1);
    }
  }

  return newItems;
};
