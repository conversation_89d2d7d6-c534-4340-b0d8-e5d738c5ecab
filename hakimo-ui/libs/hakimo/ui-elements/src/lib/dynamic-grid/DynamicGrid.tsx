/* eslint-disable max-lines */
import clsx from 'clsx';
import { AnimatePresence, motion } from 'framer-motion';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { calculateNewLayout, getNullRowsOrCols, removeColOrRow } from './utils';

interface Props {
  maxRows: number;
  maxCols: number;
  renderItem: (itemId: string, isTopRightCorner: boolean) => React.ReactNode;
}

export interface DynamicGridRef {
  addItems: (items: string[]) => void;
  removeItems: (itemIds: string[]) => void;
  getAllItems: () => string[];
}

export const DynamicGrid = forwardRef<DynamicGridRef, Props>(
  (props: Props, ref: React.Ref<DynamicGridRef>) => {
    const { maxRows, maxCols, renderItem } = props;
    const [layout, setLayout] = useState({ rows: 0, cols: 0 });
    const containerRef = useRef<HTMLDivElement>(null);
    const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

    const [gridItemIds, setGridItemIds] = useState<(string | null)[]>([]);
    const [queuedItemIds, setQueuedItemIds] = useState<string[]>([]);

    const updateSize = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth;
        const containerHeight = containerRef.current.offsetHeight;

        // Add fallback values when container dimensions are 0 or invalid
        const fallbackWidth = Math.max(containerWidth, 300);
        const fallbackHeight = Math.max(containerHeight, 200);

        setContainerSize({
          width: fallbackWidth,
          height: fallbackHeight,
        });
      }
    };
    // TODO: cross check this, if its required
    useEffect(() => {
      updateSize();
      let resizeObserver: ResizeObserver | null = null;
      // Use ResizeObserver for better container size tracking
      if (containerRef.current) {
        resizeObserver = new ResizeObserver((entries) => {
          for (const entry of entries) {
            const { width, height } = entry.contentRect;
            const fallbackWidth = Math.max(width, 300);
            const fallbackHeight = Math.max(height, 200);

            setContainerSize({
              width: fallbackWidth,
              height: fallbackHeight,
            });
          }
        });

        resizeObserver.observe(containerRef.current);
      }
      return () => {
        resizeObserver?.disconnect();
      };
      //  added escalationGroupId so update size is called when escalation screen opens and closes
    }, []);
    // }, [activeEscalations, isFullScreen]); // TODO: cross check this, if its required

    const filterAndQueueItems = useCallback(
      (newItemIds: string[]): string[] => {
        let itemsToShow: string[] = [];
        let itemsToQueue: string[] = [];
        const maxCellCount = maxRows * maxCols;
        const visibleItemCount = gridItemIds.filter(Boolean).length;
        const newItemsCount = newItemIds.length;
        const diff = maxCellCount - (visibleItemCount + newItemsCount);
        if (diff > 0) {
          itemsToShow = newItemIds;
        } else {
          const countItemsToQueue =
            visibleItemCount + newItemsCount - maxCellCount + 1;
          itemsToShow = newItemIds.slice(0, newItemsCount - countItemsToQueue);
          itemsToQueue = newItemIds.slice(newItemsCount - countItemsToQueue);
        }

        if (itemsToQueue.length > 0) {
          const updatedQueueItemIds = [...queuedItemIds];
          for (const itemId of itemsToQueue) {
            if (!updatedQueueItemIds.includes(itemId)) {
              updatedQueueItemIds.push(itemId);
            }
          }
          setQueuedItemIds(updatedQueueItemIds);
        }

        return itemsToShow;
      },
      [gridItemIds, maxCols, maxRows, queuedItemIds]
    );

    const addItemsInGrid = useCallback(
      (itemIds: string[]) => {
        const prevRows = layout.rows;
        const prevCols = layout.cols;
        const { rows: newRows, cols: newCols } = calculateNewLayout(
          gridItemIds.filter(Boolean).length + itemIds.length,
          prevRows,
          prevCols
        );

        let updatedGridItemIds = [...gridItemIds];

        // Add new columns if required
        if (newCols > prevCols) {
          for (let i = 0; i < prevRows; i++) {
            for (let j = prevCols; j < newCols; j++) {
              updatedGridItemIds.splice(i * newCols + j, 0, null);
            }
          }
        }

        // Add new rows if required
        if (newRows > prevRows) {
          const newRowsCount = newRows - prevRows;
          const newRowGroups = new Array(newRowsCount * newCols).fill(null);
          updatedGridItemIds = [...updatedGridItemIds, ...newRowGroups];
        }
        // Add new cameras to empty slots
        for (const itemIdToAdd of itemIds) {
          const emptyIndex = updatedGridItemIds.findIndex(
            (itemId) => itemId === null
          );
          if (emptyIndex !== -1) {
            updatedGridItemIds[emptyIndex] = itemIdToAdd;
          }
        }

        setLayout({ rows: newRows, cols: newCols });
        setGridItemIds(updatedGridItemIds);
      },
      [gridItemIds, layout.cols, layout.rows]
    );

    const addItems = useCallback(
      (itemIds: string[]) => {
        // const itemIds = items.map((item) => getItemId(item));
        const itemsToAdd = filterAndQueueItems(itemIds);
        if (itemsToAdd.length > 0) {
          addItemsInGrid(itemsToAdd);
        }
      },
      [filterAndQueueItems, addItemsInGrid]
    );

    const removeItems = useCallback(
      (itemIds: string[]) => {
        let updatedGridItemIds = [...gridItemIds];
        for (let i = 0; i < updatedGridItemIds.length; i++) {
          const currItemId = updatedGridItemIds[i];
          if (currItemId && itemIds.includes(currItemId)) {
            updatedGridItemIds[i] = null;
          }
        }

        const { nullRow: nullRowIndex, nullCol: nullColIndex } =
          getNullRowsOrCols(updatedGridItemIds, layout.rows, layout.cols);
        let updatedRows = layout.rows;
        let updatedCols = layout.cols;

        if (nullRowIndex !== undefined) {
          updatedGridItemIds = removeColOrRow(
            updatedGridItemIds,
            layout.rows,
            layout.cols,
            nullRowIndex,
            true
          );
          updatedRows -= 1;
        }
        if (nullColIndex !== undefined) {
          updatedGridItemIds = removeColOrRow(
            updatedGridItemIds,
            layout.rows,
            layout.cols,
            nullColIndex,
            false
          );
          updatedCols -= 1;
        }

        const removeNullGroups = (itemsUpd: Array<string | null>) =>
          itemsUpd.filter(Boolean);

        if (updatedRows > maxRows) {
          updatedRows = maxRows;
          updatedGridItemIds = removeNullGroups(updatedGridItemIds);
        }
        if (updatedCols > maxCols) {
          updatedCols = maxCols;
          updatedGridItemIds = removeNullGroups(updatedGridItemIds);
        }

        setLayout({ rows: updatedRows, cols: updatedCols });
        setGridItemIds(updatedGridItemIds);

        updateSize();
      },
      [gridItemIds, layout.cols, layout.rows, maxCols, maxRows]
    );

    useEffect(() => {
      /*
      This effect is used for setting the grid count throeshold in check.
      If there's one cell empty it will add
      If grid size is decreased, it will remove appropriate cells and add it to queue.
      If grid size is increased, it willl add cells and remove them from queue.
    */
      const visibleItems: string[] = gridItemIds.filter(Boolean) as string[];
      const maxCellCount = maxRows * maxCols;
      const cellThreshold = maxCellCount - 1;

      if (queuedItemIds.length > 0 && visibleItems.length < cellThreshold) {
        // Calculate how many items can be added
        const availableSlots = cellThreshold - visibleItems.length;
        const itemsToAdd = queuedItemIds.slice(0, availableSlots);
        const updatedQueuedItems = queuedItemIds.slice(availableSlots);
        if (itemsToAdd.length > 0) {
          addItems(itemsToAdd);
          setQueuedItemIds(updatedQueuedItems);
        }
      } else if (visibleItems.length > cellThreshold) {
        // Calculate how many items need to be removed
        // This is called when grid config is changed from high to low.
        const excessItemCount = visibleItems.length - cellThreshold;
        const itemsToRemove = visibleItems.slice(-excessItemCount);
        if (itemsToRemove.length > 0) {
          removeItems(itemsToRemove);
          setQueuedItemIds((prevQueued) => [...prevQueued, ...itemsToRemove]);
        }
      }
    }, [addItems, gridItemIds, maxCols, maxRows, queuedItemIds, removeItems]);

    const getAllItems = () => {
      // TODO: check this if ref is required
      return [...(gridItemIds.filter(Boolean) as string[]), ...queuedItemIds];
    };

    useImperativeHandle(ref, () => ({
      addItems,
      removeItems,
      getAllItems,
    }));

    const calculatePosition = useCallback(
      (index: number) => {
        const rows = layout.rows;
        const cols = layout.cols;
        const videoAspectRatio = 16 / 9;

        // Add validation to prevent division by zero
        if (
          rows === 0 ||
          cols === 0 ||
          containerSize.width === 0 ||
          containerSize.height === 0
        ) {
          return {
            left: '0px',
            top: '0px',
            width: '100px',
            height: '56px', // 16:9 aspect ratio fallback
          };
        }

        let cellWidth = containerSize.width / cols;
        let cellHeight = containerSize.height / rows;
        if (cellWidth / cellHeight > videoAspectRatio) {
          cellWidth = cellHeight * videoAspectRatio;
        } else {
          cellHeight = cellWidth / videoAspectRatio;
        }

        // Calculate the total space available for gaps
        const totalHorizontalGapSpace = containerSize.width - cellWidth * cols;
        const totalVerticalGapSpace = containerSize.height - cellHeight * rows;

        // Calculate gap sizes
        const horizontalGap = totalHorizontalGapSpace / (cols + 1);
        const verticalGap = totalVerticalGapSpace / (rows + 1);

        // Calculate the position
        const row = Math.floor(index / cols);
        const col = index % cols;

        const left = horizontalGap + col * (cellWidth + horizontalGap);
        const top = verticalGap + row * (cellHeight + verticalGap);

        return {
          left: `${left}px`,
          top: `${top}px`,
          width: `${cellWidth}px`,
          height: `${cellHeight}px`,
        };
      },
      [layout.rows, layout.cols, containerSize]
    );

    const queueLength = queuedItemIds.length;

    return (
      <div
        className="relative h-[calc(100%)] max-h-full min-h-[200px] min-w-[300px] flex-grow"
        ref={containerRef}
      >
        <AnimatePresence mode="popLayout">
          {gridItemIds.map((itemId, index) => {
            return itemId ? (
              <motion.div
                key={itemId || index}
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.4 }}
                className={clsx('absolute flex flex-col rounded-md p-[2px]')}
                style={{
                  ...calculatePosition(index),
                }}
              >
                {renderItem(itemId, index % layout.cols === layout.cols - 1)}
              </motion.div>
            ) : null;
          })}
          {queueLength > 0 && (
            <div
              style={{
                ...calculatePosition(layout.cols * layout.rows - 1),
              }}
              className={clsx(
                'dark:border-ondark-line-2 absolute flex items-center justify-center rounded-md border font-bold',
                queueLength < 5 && 'text-green-500',
                queueLength >= 5 && queueLength < 10 && 'text-orange-500',
                queueLength >= 10 && 'text-red-500'
              )}
            >
              + {queueLength} Alarms
            </div>
          )}
        </AnimatePresence>
      </div>
    );
  }
);

export default DynamicGrid;
