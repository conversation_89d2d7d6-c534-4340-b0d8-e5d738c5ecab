import { BroadcastIcon } from '@hakimo-ui/hakimo/ui-elements';
import { FilmIcon } from '@heroicons/react/24/outline';
import { RefObject } from 'react';
import { CamFeedMode } from '../../../types/common';

export const getCellMediaMenuItems = (
  currentVal: CamFeedMode,
  onChange: (val: CamFeedMode) => void
) => {
  const onClick = (type: CamFeedMode) => () => onChange(type);
  const className = 'text-primary-500 dark:text-primary-500';

  return [
    {
      name: 'LIVE',
      onClick: onClick(CamFeedMode.LIVE),
      className: currentVal === CamFeedMode.LIVE ? className : '',
      icon: (
        <BroadcastIcon className="h-4 w-4 animate-pulse rounded-lg bg-red-500 fill-white" />
      ),
    },
    {
      name: 'Event Video',
      onClick: onClick(CamFeedMode.EVENT_VIDEO),
      className: currentVal === CamFeedMode.EVENT_VIDEO ? className : '',
      icon: <FilmIcon className="h-4 w-4" />,
    },
  ];
};

/**
 * Returns X offset with which component has to render
 * @param position
 * @param timelineRef
 * @returns
 */
export const getHoverTranslate = (
  position: number,
  timelineRef: RefObject<HTMLDivElement>
) => {
  if (!timelineRef.current || position === null) return '50%';

  const timelineWidth = timelineRef.current.offsetWidth;
  const imageWidth = timelineWidth / 2; // Assuming image width is half of timeline width
  const hoverX = (position / 100) * timelineWidth;

  if (hoverX < imageWidth / 2) {
    return '0%'; // Align to left edge
  } else if (hoverX > timelineWidth - imageWidth / 2) {
    return '100%'; // Align to right edge
  } else {
    return '50%'; // Center
  }
};
