import { Placement } from '@floating-ui/react-dom';
import { SmartTooltip } from '@hakimo-ui/shared/ui-base';
import { BoltIcon, LightBulbIcon } from '@heroicons/react/24/solid';
import clsx from 'clsx';
import { useMemo } from 'react';
import { LLMEvent } from '../../../types/common';

interface Props {
  cameraGroupId: string;
  visible?: boolean;
  placement?: Placement;
  llmEvent?: LLMEvent;
}

export function AIRecommendationIcon(props: Props) {
  const { visible, placement, llmEvent } = props;

  const isResolve = useMemo(() => {
    if (llmEvent) {
      const recommendation = llmEvent.recommendation;
      return recommendation.toLowerCase().includes('resolve');
    } else {
      return false;
    }
  }, [llmEvent]);

  return (
    <div>
      {llmEvent && (
        <SmartTooltip
          title={llmEvent.recommendation}
          isOpen={visible}
          placement={placement}
          icon={<LightBulbIcon className="text-hakimo-yellow h-5 w-5" />}
          titleClassname={clsx(isResolve ? 'text-green-500' : 'text-red-500')}
        >
          <BoltIcon
            className={clsx(
              'h-5 w-5 ',
              isResolve ? 'text-green-500' : 'text-red-500'
            )}
          />
        </SmartTooltip>
      )}
    </div>
  );
}

export default AIRecommendationIcon;
