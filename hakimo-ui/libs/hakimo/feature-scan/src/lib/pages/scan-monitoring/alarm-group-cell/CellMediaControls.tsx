import { UnifiedAlarm } from '@hakimo-ui/hakimo/types';
import {
  BroadcastIcon,
  FullScreenExitIcon,
  FullScreenIcon,
  MenuDropdown,
} from '@hakimo-ui/hakimo/ui-elements';
import { FilmIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { CamFeedMode } from '../../../types/common';
import { getTenantColor } from '../utils';
import CellMediaTimeline from './CellMediaTimeline';
import { getCellMediaMenuItems } from './utils';

interface Props {
  alarms: UnifiedAlarm[];
  camFeedMode: CamFeedMode;
  onChangeCamFeedMode: (val: CamFeedMode) => void;
  tenantId: string;
  onUpdatePlaybackSrc: (params: string) => void;
  isFullScreen: boolean;
  toggleFullscreen: () => void;
  showSeeker: boolean;
  currentAlarmId: string;
  timeZone: string;
}

export function CellMediaControls(props: Props) {
  const {
    alarms,
    camFeedMode,
    onChangeCamFeedMode,
    tenantId,
    onUpdatePlaybackSrc,
    isFullScreen,
    showSeeker,
    currentAlarmId,
    timeZone,
    toggleFullscreen,
  } = props;

  const firstAlarm = alarms[0];
  const { cameraName } = firstAlarm;

  const menuButton =
    camFeedMode === CamFeedMode.EVENT_VIDEO ? (
      <FilmIcon className="text-ondark-text-1 h-4 w-4 rounded-lg fill-white" />
    ) : (
      <div className="flex animate-pulse items-center gap-1 text-red-500">
        <BroadcastIcon
          className={clsx(
            'h-4 w-4 rounded-lg fill-white',
            camFeedMode === CamFeedMode.LIVE ? 'bg-red-500' : 'bg-gray-500'
          )}
        />
      </div>
    );

  return (
    <div className="from-dark-surface absolute bottom-0 left-0 right-0 cursor-pointer rounded-md bg-gradient-to-t">
      {showSeeker && (
        <CellMediaTimeline
          alarms={alarms}
          onUpdatePlaybackSrc={onUpdatePlaybackSrc}
          currentAlarmId={currentAlarmId}
          timeZone={timeZone}
        />
      )}
      <div className="flex items-center justify-between px-1 py-[1px]">
        <MenuDropdown
          position="top"
          items={getCellMediaMenuItems(camFeedMode, onChangeCamFeedMode)}
          menuButton={menuButton}
          isMinimized
          theme="dark"
        />
        <span
          title={tenantId}
          className="truncate text-xs"
          style={{ color: getTenantColor(tenantId || '') }}
        >
          {tenantId}
        </span>
        <span
          title={cameraName}
          className="truncate text-xs"
          style={{ color: getTenantColor(tenantId || '') }}
        >
          {cameraName}
        </span>
        <button
          onClick={toggleFullscreen}
          className="rounded-md focus:ring-offset-0 enabled:hover:bg-slate-800/50"
        >
          {isFullScreen ? (
            <FullScreenExitIcon className="fill-dark-text h-5 w-5" />
          ) : (
            <FullScreenIcon className="fill-dark-text h-5 w-5" />
          )}
        </button>
      </div>
    </div>
  );
}

export default CellMediaControls;
