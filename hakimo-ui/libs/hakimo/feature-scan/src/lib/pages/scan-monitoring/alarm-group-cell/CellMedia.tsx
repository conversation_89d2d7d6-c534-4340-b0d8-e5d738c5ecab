/* eslint-disable max-lines */
import { Frame, UnifiedAlarm } from '@hakimo-ui/hakimo/types';
import { FramePlayer, WebrtcPlayer } from '@hakimo-ui/hakimo/ui-elements';
import { useFullscreen } from '@hakimo-ui/hakimo/util';
import clsx from 'clsx';
import { useLayoutEffect, useMemo, useRef, useState } from 'react';
import { CamFeedMode } from '../../../types/common';
import CamFeedActions from '../cam-feed/CamFeedActions';
import CamPlaybackFeed from '../cam-feed/CamPlaybackFeed';
import CellMediaControls from './CellMediaControls';

interface Props {
  alarms: UnifiedAlarm[];
  onDoubleClick: () => void;
  timeZone: string;
  tenantId: string;
  onClickSafe?: () => void;
  onClickSnooze?: () => void;
  onClickInvestigate?: () => void;
}

export function CellMedia(props: Props) {
  const {
    alarms,
    onDoubleClick,
    timeZone,
    tenantId,
    onClickSafe,
    onClickSnooze,
    onClickInvestigate,
  } = props;

  const [mode, setMode] = useState<CamFeedMode>(CamFeedMode.EVENT_VIDEO);
  const containerRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });
  const [playbackVideoSrc, setPlaybackVideoSrc] = useState<string>();
  const [isHovered, setIsHovered] = useState(false);
  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);

  const { isFullScreen, toggleFullScreen } = useFullscreen(containerRef);

  const firstAlarm = alarms[0];
  const { cameraLiveStreamUrl, cameraPlayBackUrl } = firstAlarm;

  useLayoutEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      if (entries.length > 0) {
        const entry = entries[0];
        const parent = entry.target;
        setSize({
          width: parent ? parent.clientWidth - 4 : 0,
          height: parent ? parent.clientHeight - 4 : 0,
        });
      }
    });

    if (containerRef.current) {
      const parentDiv = containerRef.current.parentElement;
      parentDiv && resizeObserver.observe(parentDiv);
    }

    return () => {
      if (containerRef.current) {
        const parentDiv = containerRef.current.parentElement;
        parentDiv && resizeObserver.unobserve(parentDiv);
      }
    };
  }, []);

  const hideActionText =
    containerRef.current && containerRef.current.clientWidth < 280;

  const onUpdatePlaybackSrc = (params: string) => {
    setPlaybackVideoSrc(`${cameraPlayBackUrl}&${params}`);
    setMode(CamFeedMode.PLAYBACK);
  };

  const handleDoubleClick = () => onDoubleClick();
  const handleMouseOver = () => setIsHovered(true);
  const handleMouseOut = () => setIsHovered(false);

  const frames = useMemo(() => {
    return alarms
      .sort((a, b) => a.alarmStartTime - b.alarmStartTime)
      .reduce((acc, alarm) => {
        const alarmFrames = alarm.media.frames;
        if (alarmFrames) {
          acc.push(...alarmFrames);
        }
        return acc;
      }, [] as Frame[]);
  }, [alarms]);

  const onFrameChange = (idx: number) => setCurrentFrameIndex(idx);

  return (
    <div
      className={clsx(
        'group/feed bg-ondark-bg-2 relative max-h-full max-w-full flex-grow rounded-md',
        isFullScreen && ''
      )}
      ref={containerRef}
      onDoubleClick={handleDoubleClick}
      onMouseOver={handleMouseOver}
      onMouseOut={handleMouseOut}
      style={{
        width: size.width,
        height: size.height,
      }}
    >
      {mode === CamFeedMode.LIVE && (
        <div className="h-full">
          <WebrtcPlayer url={cameraLiveStreamUrl} showControls={false} />
        </div>
      )}
      {mode === CamFeedMode.PLAYBACK && playbackVideoSrc && (
        <div className="h-full">
          <CamPlaybackFeed
            onUpdatePlaybackSrc={onUpdatePlaybackSrc}
            videoSrc={playbackVideoSrc}
          />
        </div>
      )}
      {mode === CamFeedMode.EVENT_VIDEO && (
        <FramePlayer
          frames={frames}
          currentFrameIndex={currentFrameIndex}
          onFrameChange={onFrameChange}
          timeZone={timeZone}
        />
      )}
      <CellMediaControls
        alarms={alarms}
        camFeedMode={mode}
        onChangeCamFeedMode={setMode}
        tenantId={tenantId}
        onUpdatePlaybackSrc={onUpdatePlaybackSrc}
        isFullScreen={isFullScreen}
        toggleFullscreen={toggleFullScreen}
        showSeeker={isHovered}
        currentAlarmId={frames[currentFrameIndex]?.id}
        timeZone={timeZone}
      />
      <CamFeedActions
        hideActionText={!!hideActionText}
        onClickInvestigate={onClickInvestigate}
        onClickSafe={onClickSafe}
        onClickSnooze={onClickSnooze}
      />
    </div>
  );
}
