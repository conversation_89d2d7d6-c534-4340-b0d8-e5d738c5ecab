/* eslint-disable max-lines */
import { Frame, UnifiedAlarm } from '@hakimo-ui/hakimo/types';
import clsx from 'clsx';
import { useRef, useState } from 'react';
import { formatTime, HOVER_MARGIN_TOP, TIMELINE_HEIGHT } from '../utils';
import { getHoverTranslate } from './utils';

interface Props {
  alarms: UnifiedAlarm[];
  onUpdatePlaybackSrc?: (params: string) => void;
  currentAlarmId?: string;
  timeZone: string;
}

interface FrameWithAlarmData extends Frame {
  isPastEvent?: boolean;
  isDedupEvent?: boolean;
  severity: string;
}

export function CellMediaTimeline(props: Props) {
  const { alarms, onUpdatePlaybackSrc, currentAlarmId, timeZone } = props;

  const timelineRef = useRef<HTMLDivElement>(null);
  const [hoverImagePosition, setHoverImagePosition] = useState<number | null>(
    null
  );
  const [hoverFrame, setHoverFrame] = useState<FrameWithAlarmData>();
  const [hoverTimePosition, setHoverTimePosition] = useState<number | null>(
    null
  );

  const isHoverEnabled = !!onUpdatePlaybackSrc;

  const tenMinutesAgo = new Date(new Date().getTime() - 10 * 60 * 1000);

  const getFramePosition = (frame: FrameWithAlarmData) => {
    const timeDiff = frame.timestamp - tenMinutesAgo.getTime();
    return (timeDiff / (10 * 60 * 1000)) * 100;
  };

  const relevantAlarms =
    alarms?.filter((alarm) => alarm.alarmStartTime > tenMinutesAgo.getTime()) ??
    [];

  const onHoverFrame =
    (frame: FrameWithAlarmData) =>
    (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
      if (timelineRef.current) {
        const rect = timelineRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = (x / rect.width) * 100;
        setHoverImagePosition(percentage);
        setHoverFrame(frame);
      }
    };

  const onHoverTimeline = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (timelineRef.current) {
      const rect = timelineRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = (x / rect.width) * 100;
      setHoverTimePosition(percentage);
    }
  };

  const onFrameMouseLeave: React.MouseEventHandler<HTMLDivElement> = () =>
    setHoverImagePosition(null);

  const onTimelineMouseLeave: React.MouseEventHandler<HTMLDivElement> = () =>
    setHoverTimePosition(null);

  const getHoverTime = (position: number) => {
    if (position === null) return null;
    const hoverTime = new Date(
      tenMinutesAgo.getTime() + (position / 100) * 10 * 60 * 1000
    );
    return formatTime(hoverTime, timeZone);
  };

  const onClickTimeline: React.MouseEventHandler<HTMLDivElement> = (e) => {
    if (timelineRef.current) {
      const rect = timelineRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = (x / rect.width) * 100;
      const hoverTime = new Date(
        tenMinutesAgo.getTime() + (percentage / 100) * 10 * 60 * 1000
      );
      const hoverTimeISOFormat = hoverTime.toISOString();
      const duration = Math.floor((Date.now() - hoverTime.getTime()) / 1000);

      const params = new URLSearchParams();
      params.append('start', hoverTimeISOFormat);
      params.append('duration', String(duration));

      onUpdatePlaybackSrc?.(params.toString());
    }
  };

  const getFrameColor = (frame: FrameWithAlarmData) => {
    const { severity, isDedupEvent, isPastEvent, id } = frame;

    if (currentAlarmId === id) {
      return 'bg-green-500';
    } else if (isPastEvent) {
      return 'bg-gray-400'; //white
    } else if (isDedupEvent) {
      return 'bg-teal-300';
    } else if (severity === 'high') {
      return 'bg-orange-300';
    } else {
      return 'bg-red-500';
    }
  };

  const allFramesWithAlarmData = relevantAlarms.reduce((acc, alarm) => {
    const frames = alarm.media.frames ?? [];
    const updatedFrames = frames.map((frame) => ({
      ...frame,
      isPastEvent: alarm.isPastEvent,
      isDedupEvent: alarm.isDedupEvent,
      severity: alarm.severity,
    }));
    return [...acc, ...updatedFrames];
  }, [] as FrameWithAlarmData[]);

  return (
    <>
      {hoverImagePosition && hoverFrame && (
        <div
          className="absolute z-20 w-1/2"
          style={{
            left: `${hoverImagePosition}%`,
            bottom: `${TIMELINE_HEIGHT + HOVER_MARGIN_TOP}px`,
            transform: `translateX(-${getHoverTranslate(
              hoverImagePosition,
              timelineRef
            )})`,
          }}
        >
          <span className="bg-ondark-bg-2 absolute left-1 top-1 flex gap-2 rounded-md px-2 text-xs text-white">
            <span>{formatTime(new Date(hoverFrame.timestamp), timeZone)}</span>
            {hoverFrame.isPastEvent && (
              <span className="text-[10px]">Past event</span>
            )}
            {hoverFrame.isDedupEvent && (
              <span className="text-[10px]">Dedup event</span>
            )}
          </span>
          <img
            src={hoverFrame.url}
            alt="event frame"
            className="border-ondark-text-1 rounded-md border"
          />
        </div>
      )}
      {hoverTimePosition && (
        <div
          className="bg-ondark-bg-3/70 absolute cursor-pointer rounded px-2 text-[9px] text-white"
          style={{
            left: `${hoverTimePosition}%`,
            transform: `translateX(-${getHoverTranslate(
              hoverTimePosition,
              timelineRef
            )})`,
            bottom: `${TIMELINE_HEIGHT + HOVER_MARGIN_TOP}px`,
          }}
        >
          {getHoverTime(hoverTimePosition)}
        </div>
      )}
      <div
        ref={timelineRef}
        className={clsx('relative cursor-pointer rounded', 'bg-ondark-bg-1/80')}
        style={{ height: `${TIMELINE_HEIGHT}px` }}
        onMouseMoveCapture={isHoverEnabled ? onHoverTimeline : undefined}
        onMouseOverCapture={isHoverEnabled ? onHoverTimeline : undefined}
        onMouseLeave={onTimelineMouseLeave}
        onClick={onClickTimeline}
      >
        {allFramesWithAlarmData.map((frame, index) => (
          <div
            key={index}
            onMouseOverCapture={onHoverFrame(frame)}
            onMouseLeave={onFrameMouseLeave}
            className={clsx(
              'absolute top-0 h-full w-[0.12rem] transition-transform',
              getFrameColor(frame)
            )}
            style={{ left: `${getFramePosition(frame)}%` }}
          ></div>
        ))}

        {hoverTimePosition && (
          <div
            className="bg-primary-500 pointer-events-none absolute z-10 w-[0.12rem]"
            style={{
              left: `${hoverTimePosition}%`,
              height: `${TIMELINE_HEIGHT}px`,
            }}
          ></div>
        )}
      </div>
    </>
  );
}

export default CellMediaTimeline;
