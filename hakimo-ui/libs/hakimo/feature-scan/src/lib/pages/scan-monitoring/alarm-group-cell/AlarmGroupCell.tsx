/* eslint-disable max-lines */
import { UnifiedAlarmGroup } from '@hakimo-ui/hakimo/types';
import { Button } from '@hakimo-ui/shared/ui-base';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { useMemo, useState } from 'react';
import { useScanWsMessageHandler } from '../../../hooks/useScanWsMessageHandler';
import { CamAction, EscalationState, LLMEvent } from '../../../types/common';
import { VisionEventType } from '../../../types/event';
import AIRecommendationIcon from './AIRecommendationIcon';
import { CellMedia } from './CellMedia';

interface Props {
  alarmGroup: UnifiedAlarmGroup;
  onGroupAction: (
    cameraId: string,
    alarmGroupId: string,
    cameraGroupId: string,
    actionType: CamAction,
    comment?: string
  ) => void;
  escalationState?: EscalationState;
  // onResolveEscalation: (comment?: string) => void;
  isTopRightCorner: boolean;
  openInvestigationModal: () => void;
}

export function AlarmGroupCell(props: Props) {
  const {
    alarmGroup,
    onGroupAction,
    escalationState,
    // onResolveEscalation,
    isTopRightCorner,
    openInvestigationModal,
  } = props;

  const [shownCamIndex, setShownCamIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const [llmEvent, setLLMEvent] = useState<LLMEvent>();
  const firstAlarm = alarmGroup.alarms[0];

  const handleLLMevents = (data: LLMEvent[]) => {
    const relevantEvent = data.findLast(
      (ev) => ev.alarm_group_id === alarmGroup.id
    );
    if (relevantEvent) {
      setLLMEvent(relevantEvent);
    }
  };

  useScanWsMessageHandler(VisionEventType.LLM_EVENT, handleLLMevents);

  const alarmCams: string[] = useMemo(() => {
    return Array.from(alarmGroup?.camToAlarmsMap?.keys() ?? []);
  }, [alarmGroup?.camToAlarmsMap]);

  const selectedCamId = alarmCams[shownCamIndex];

  const handleCellAction = (actionType: CamAction) => (comment?: string) => {
    if (actionType === CamAction.SAFE) {
      setLLMEvent(undefined);
    }
    onGroupAction(
      selectedCamId ?? 'unknown-cam-id',
      alarmGroup.id,
      firstAlarm?.cameraGroupId ?? 'unknown-cam-group-id',
      actionType,
      comment
    );
  };

  const isHighPriorityEvent = useMemo(() => {
    return alarmGroup.alarms.some((alarm) => alarm.severity === 'high');
  }, [alarmGroup.alarms]);

  const getBorderColor = () => {
    if (escalationState?.alarmGroupIds.includes(alarmGroup.id)) {
      return 'rounded-md border-[3px] border-purple-600';
    } else if (isHighPriorityEvent) {
      return 'rounded-md border-[3px] border-red-600 dark:border-orange-600';
    }
    return '';
  };

  const handleMouseOver = () => setIsHovered(true);
  const handleMouseOut = () => setIsHovered(false);
  const onClickLeft = () =>
    shownCamIndex > 0 && setShownCamIndex(shownCamIndex - 1);
  const onClickRight = () =>
    shownCamIndex < alarmCams.length - 1 && setShownCamIndex(shownCamIndex + 1);

  const selectedCamAlarms = useMemo(() => {
    return alarmGroup.alarms.filter(
      (alarm) => alarm.cameraId === selectedCamId
    );
  }, [alarmGroup.alarms, selectedCamId]);

  return (
    <div
      className={clsx(
        'relative max-h-full max-w-full flex-grow rounded-md px-[1px]',
        getBorderColor()
      )}
      onMouseOver={handleMouseOver}
      onMouseOut={handleMouseOut}
    >
      <span
        title={firstAlarm.cameraGroupName}
        className="text-ondark-text-1 from-dark-surface absolute left-[2px] top-[2px] z-10 w-2/3 truncate rounded-md bg-gradient-to-b p-[2px] text-xs"
      >
        {firstAlarm.cameraGroupName}
      </span>
      {llmEvent && (
        <div
          className={clsx(
            'bg-ondark-bg-1/60 absolute top-5 rounded-full p-[1px]',
            isTopRightCorner ? 'left-1' : 'right-1',
            isHovered ? 'z-20' : 'z-10'
          )}
        >
          <AIRecommendationIcon
            cameraGroupId={firstAlarm.cameraGroupId}
            visible={isHovered}
            placement={isTopRightCorner ? 'left-start' : undefined}
            llmEvent={llmEvent}
          />
        </div>
      )}
      <span className="text-ondark-text-1 from-dark-surface absolute right-[2px] top-[2px] z-10 rounded-md bg-gradient-to-b p-[2px] text-xs">{`${
        shownCamIndex + 1
      }/${alarmCams.length}`}</span>
      {shownCamIndex > 0 && (
        <Button
          disabled={shownCamIndex < 1}
          variant="icon"
          onClick={onClickLeft}
          className="absolute left-1 top-[40%] z-10"
        >
          <ChevronLeftIcon className="h-3 w-3" />
        </Button>
      )}
      {shownCamIndex < alarmCams.length - 1 && (
        <Button
          disabled={shownCamIndex >= alarmCams.length - 1}
          variant="icon"
          onClick={onClickRight}
          className="absolute right-1 top-[40%] z-10"
        >
          <ChevronRightIcon className="h-3 w-3" />
        </Button>
      )}
      {selectedCamId && (
        <div
          key={selectedCamId}
          className="flex h-full w-full items-center justify-center rounded-md"
        >
          <CellMedia
            key={selectedCamId}
            tenantId={alarmGroup.tenantId}
            alarms={selectedCamAlarms}
            timeZone={alarmGroup.location.timezone ?? 'UTC'}
            onClickSafe={
              escalationState ? undefined : handleCellAction(CamAction.SAFE)
            }
            onClickSnooze={
              escalationState ? undefined : handleCellAction(CamAction.SNOOZE)
            }
            onClickInvestigate={openInvestigationModal}
            onDoubleClick={openInvestigationModal}
          />
        </div>
      )}
    </div>
  );
}

export default AlarmGroupCell;
