/* eslint-disable max-lines */
import { UnifiedAlarmGroup } from '@hakimo-ui/hakimo/types';
import { DynamicGrid, DynamicGridRef } from '@hakimo-ui/hakimo/ui-elements';
import { trackEvent, useScanWs, useUser } from '@hakimo-ui/hakimo/util';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { CamAction, EscalationState, GridConfig } from '../../types/common';
import { VisionOutboundType } from '../../types/event';
import { AlarmGroupCell } from './alarm-group-cell/AlarmGroupCell';
import InvestigateViewModal from './investigate-view/InvestigateViewModal';

export interface ScanAlarmsGridRef {
  addAlarmGroups: (alarmGroupIds: string[]) => void;
  getActiveGroups: () => string[];
}

interface Props {
  alarmGroupIdToDetailsMap: Record<string, UnifiedAlarmGroup>;
  gridConfig: GridConfig;
  onCellAction: (
    cameraId: string,
    alarmGroupId: string,
    cameraGroupId: string,
    actionType: CamAction,
    comment?: string
  ) => void;
  isScanPaused: boolean;
  activeEscalations: EscalationState[];
  onResolveEscalation: (comment: string, alarmGroupId: string) => void;
}

const ScanAlarmsGrid = forwardRef<ScanAlarmsGridRef, Props>(
  (props: Props, ref) => {
    const {
      alarmGroupIdToDetailsMap,
      gridConfig,
      onCellAction,
      isScanPaused,
      activeEscalations,
      onResolveEscalation,
    } = props;
    const { send: sendScanWS } = useScanWs();
    const user = useUser();
    const dynamicGridRef = useRef<DynamicGridRef>(null);
    const [showInvestigationModal, setShowInvestigationModal] = useState(false);
    const [
      alarmGroupIdToShowInvestigationModal,
      setAlarmGroupIdToShowInvestigationModal,
    ] = useState<string | null>(null);
    const requestAlarms = useCallback(
      (count: number) => {
        if (!isScanPaused) {
          sendScanWS?.({
            count: count,
            operator_id: user.email,
            event_type: VisionOutboundType.LIST_ALARMS,
          });
        }
      },
      [isScanPaused, sendScanWS, user.email]
    );

    useEffect(() => {
      requestAlarms(gridConfig.cols * gridConfig.rows - 1);

      const intervalId = window.setInterval(() => {
        const maxGroupCount = gridConfig.cols * gridConfig.rows - 1;
        const currentGroupCount =
          dynamicGridRef.current?.getAllItems().length ?? 0;
        const availableSpace = maxGroupCount - currentGroupCount;
        if (availableSpace > 0) {
          requestAlarms(availableSpace);
        }
      }, 5000);

      return () => {
        if (intervalId) clearInterval(intervalId);
      };
    }, [gridConfig.cols, gridConfig.rows, requestAlarms]);

    const addAlarmGroups = (alarmGroupIds: string[]) => {
      try {
        dynamicGridRef.current?.addItems(alarmGroupIds);
      } catch (error) {
        trackEvent('scan_error_adding_alarm_group', {
          error: error,
        });
      }
    };

    const removeAlarmGroupFromGrid = (alarmGroupId: string) => {
      dynamicGridRef.current?.removeItems([alarmGroupId]);
    };

    const getActiveGroups = () => {
      return dynamicGridRef.current?.getAllItems() ?? [];
    };

    useImperativeHandle(ref, () => ({
      addAlarmGroups,
      getActiveGroups,
    }));

    const onGroupAction = (
      cameraId: string,
      alarmGroupId: string,
      cameraGroupId: string,
      actionType: CamAction,
      comment?: string
    ) => {
      switch (actionType) {
        case CamAction.SAFE:
          trackEvent('event_marked_safe', {
            alarmGroupId: alarmGroupId,
            tenantId: alarmGroupIdToDetailsMap[alarmGroupId].tenantId,
          });
          removeAlarmGroupFromGrid(alarmGroupId);
          break;
        case CamAction.ESCALATE:
          trackEvent('event_escalated', {
            alarmGroupId: alarmGroupId,
            tenantId: alarmGroupIdToDetailsMap[alarmGroupId].tenantId,
          });
          break;
        case CamAction.INVESTIGATE:
          trackEvent('event_investigate', {
            alarmGroupId: alarmGroupId,
            tenantId: alarmGroupIdToDetailsMap[alarmGroupId].tenantId,
          });
          break;
        default:
      }
      onCellAction(cameraId, alarmGroupId, cameraGroupId, actionType, comment);
    };

    const handleInvestigationModal = (alarmGroupId: string) => () => {
      setShowInvestigationModal(true);
      setAlarmGroupIdToShowInvestigationModal(alarmGroupId);
    };

    const handleModalClose = () => {
      setShowInvestigationModal(false);
      setAlarmGroupIdToShowInvestigationModal(null);
    };

    const handleCamActionFromInvestigationModal =
      (alarmGroupId: string) =>
      (actionType: CamAction) =>
      (comment?: string) => {
        const alarmGroupDetails = alarmGroupIdToDetailsMap[alarmGroupId];
        onGroupAction(
          alarmGroupDetails.alarms[0].cameraId,
          alarmGroupId,
          alarmGroupDetails.alarms[0].cameraGroupId,
          actionType,
          comment
        );
      };

    const handleResolveEscalation =
      (alarmGroupId: string) => (comment?: string) => {
        removeAlarmGroupFromGrid(alarmGroupId);
        comment && onResolveEscalation(comment, alarmGroupId);
      };

    return (
      <>
        <DynamicGrid
          ref={dynamicGridRef}
          maxCols={gridConfig.cols}
          maxRows={gridConfig.rows}
          renderItem={(alarmGroupId, isTopRightCorner) => (
            <AlarmGroupCell
              alarmGroup={alarmGroupIdToDetailsMap[alarmGroupId]}
              onGroupAction={onGroupAction}
              isTopRightCorner={isTopRightCorner}
              openInvestigationModal={handleInvestigationModal(alarmGroupId)}
            />
          )}
        />

        {showInvestigationModal && alarmGroupIdToShowInvestigationModal && (
          <InvestigateViewModal
            alarmGroup={
              alarmGroupIdToDetailsMap[alarmGroupIdToShowInvestigationModal]
            }
            onClose={handleModalClose}
            handleCamAction={handleCamActionFromInvestigationModal(
              alarmGroupIdToShowInvestigationModal
            )}
            escalationState={activeEscalations.find((escalation) =>
              escalation.alarmGroupIds.includes(
                alarmGroupIdToShowInvestigationModal
              )
            )}
            onCreateEscalation={handleCamActionFromInvestigationModal(
              alarmGroupIdToShowInvestigationModal
            )(CamAction.ESCALATE)}
            onResolveEscalation={handleResolveEscalation(
              alarmGroupIdToShowInvestigationModal
            )}
          />
        )}
      </>
    );
  }
);

export default ScanAlarmsGrid;
