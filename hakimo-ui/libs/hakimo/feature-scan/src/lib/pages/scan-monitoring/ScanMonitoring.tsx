/* eslint-disable max-lines */
import {
  useCameras,
  useCreateScanEscalation,
  useInvalidateAIRecommendationCache,
} from '@hakimo-ui/hakimo/data-access';
import { Camera, CreateEscalationResponse } from '@hakimo-ui/hakimo/types';
import { LoadingIndicator } from '@hakimo-ui/hakimo/ui-elements';
import {
  toast,
  useFullscreen,
  useScanWs,
  useUser,
} from '@hakimo-ui/hakimo/util';
import { Alert, Selectable } from '@hakimo-ui/shared/ui-base';
import clsx from 'clsx';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useScanWsMessageHandler } from '../../hooks/useScanWsMessageHandler';
import { EscalationState } from '../../types/common';
import {
  GetEscalationsResponse,
  VisionEventType,
  VisionOutboundPayload,
  VisionOutboundType,
} from '../../types/event';
import { getQueryParams } from '../utils';
import MultiAlarmsMonitor from './MultiAlarmsMonitor';

interface Props {
  visionTenants: Selectable[];
}
export function ScanMonitoring(props: Props) {
  const { visionTenants } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  const { isFullScreen, toggleFullScreen } = useFullscreen(containerRef);
  const pendingEscalationMessageRef = useRef<VisionOutboundPayload | null>(
    null
  );
  const { send: sendScanWS } = useScanWs();
  const sendMessageInWS = useCallback(
    (message: VisionOutboundPayload) => {
      sendScanWS?.(message);
    },
    [sendScanWS]
  );

  const handleEscalationCreated = useCallback(
    (data: CreateEscalationResponse) => {
      if (pendingEscalationMessageRef.current) {
        const message = pendingEscalationMessageRef.current;
        const escalationId = data.escalation_id;
        const updatedEscalationMessage = {
          ...message,
          additional_data: {
            ...message?.additional_data,
            escalation_id: escalationId,
            escalation_open_timestamp: Date.now(),
          },
        };
        sendMessageInWS?.(updatedEscalationMessage);
        pendingEscalationMessageRef.current = null;
      }
      toast('Incident escalated successfully');
    },
    [sendMessageInWS]
  );

  const {
    mutate: createEscalation,
    isError,
    error,
  } = useCreateScanEscalation(handleEscalationCreated);
  const user = useUser();

  const [activeEscalations, setActiveEscalations] = useState<EscalationState[]>(
    []
  );

  const handleGetEscalations = (data: GetEscalationsResponse) =>
    setActiveEscalations(data.escalations);

  useScanWsMessageHandler(
    VisionEventType.GET_ESCALATIONS,
    handleGetEscalations
  );

  const {
    data: allCamerasDto,
    isError: isCamerasError,
    isLoading: isCamerasLoading,
  } = useCameras(getQueryParams(visionTenants, 'tenants'));
  const allCameras = useMemo(
    () => allCamerasDto?.items || [],
    [allCamerasDto?.items]
  );

  const handleEscalation = (camera: Camera, message: VisionOutboundPayload) => {
    // TODO: Think of a better way to handle it as SOP is moved to Investigation view
    const cameraId = camera.id;
    const locationId = String(camera.location?.id) || '';
    pendingEscalationMessageRef.current = message;
    createEscalation({
      camera_id: cameraId,
      location_id: locationId,
    });
  };

  const { invalidateForAlarm } = useInvalidateAIRecommendationCache();

  const onResolveEscalation = useCallback(
    (
      resolutionComment: string,
      cameraGroupId: string,
      alarmGroupId: string
    ) => {
      const escalation = activeEscalations.find((activeEsc) =>
        activeEsc.alarmGroupIds?.includes(alarmGroupId)
      );
      if (!escalation) {
        // eslint-disable-next-line no-console
        console.error('No escalation found for the given groupId', {
          cameraGroupId,
          alarmGroupId,
        });
        return;
      }
      // send message in websocket once escalation is resolved
      const message = {
        // alarm_group_ids: [],
        alarm_group_id: alarmGroupId,
        camera_id: '',
        group_id: cameraGroupId,
        event_type: VisionOutboundType.ESCALATION_CLOSE,
        tenant_id: escalation.tenantId,
        additional_data: {
          username: user.email,
          escalation_open_timestamp: escalation.escalationOpenTimestamp, // add escalation open timestamp
          event_timestamp: Date.now(),
          escalation_id: escalation.escalationId,
          resolution_status: resolutionComment ?? 'cancelled',
          // camera_id_to_event_id: camToEventId,
          comment: resolutionComment,
        },
      };
      sendMessageInWS(message);
      invalidateForAlarm(alarmGroupId);
    },
    [activeEscalations, sendMessageInWS, user.email, invalidateForAlarm]
  );
  return (
    <div
      ref={containerRef}
      className={clsx(
        'bg-onlight-bg-1 dark:bg-ondark-bg-1 flex flex-grow flex-col overflow-hidden p-4',
        isFullScreen ? 'top-0' : 'top-20',
        // Add responsive handling for small screens
        'min-h-0 min-w-0 sm:min-h-[300px] sm:min-w-[400px]'
      )}
    >
      {isError && (
        <Alert className="m-4" type="error">
          {error.message}
        </Alert>
      )}
      {isCamerasError && (
        <div className="whitespace-nowrap px-8 py-2">
          <Alert type="error">Error loading cameras. Please try again.</Alert>
        </div>
      )}
      {!isError && !isCamerasError && isCamerasLoading && (
        <div className="z-10 flex h-full w-full items-center justify-center">
          <LoadingIndicator text="Loading Cameras..." />
        </div>
      )}
      {!isError &&
        !isCamerasError &&
        !isCamerasLoading &&
        allCameras.length === 0 && (
          <div className="z-10 flex h-full w-full items-center justify-center">
            No cameras found.
          </div>
        )}
      {!isError && !isCamerasError && allCameras.length > 0 && (
        <MultiAlarmsMonitor
          isFullScreen={isFullScreen}
          toggleFullScreen={toggleFullScreen}
          onEscalate={handleEscalation}
          activeEscalations={activeEscalations}
          onResolveEscalation={onResolveEscalation}
          allCameras={allCameras}
        />
      )}
    </div>
  );
}
