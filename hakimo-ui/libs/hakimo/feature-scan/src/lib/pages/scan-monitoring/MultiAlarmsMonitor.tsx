/* eslint-disable max-lines */
import { useInvalidateAIRecommendationCache } from '@hakimo-ui/hakimo/data-access';
import {
  Camera,
  UnifiedAlarm,
  UnifiedAlarmGroup,
} from '@hakimo-ui/hakimo/types';
import { LoadingIndicator } from '@hakimo-ui/hakimo/ui-elements';
import {
  toast,
  useLocalStorage,
  useScanWs,
  useUser,
} from '@hakimo-ui/hakimo/util';
import { <PERSON><PERSON>, Button } from '@hakimo-ui/shared/ui-base';
import { PlayIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useScanWsMessageHandler } from '../../hooks/useScanWsMessageHandler';
import { CamAction, EscalationState, GridConfig } from '../../types/common';
import {
  VisionEventType,
  VisionOutboundPayload,
  VisionOutboundType,
} from '../../types/event';
import ScanAlarmsGrid, { ScanAlarmsGridRef } from './ScanAlarmsGrid';
import ScanToolbar from './ScanToolbar';
import { updatePropertyInUnifiedAlarm } from './utils';

interface Props {
  isFullScreen: boolean;
  toggleFullScreen: () => void;
  onEscalate: (camera: Camera, message: VisionOutboundPayload) => void;
  activeEscalations: EscalationState[];
  onResolveEscalation: (
    comment: string,
    cameraGroupId: string,
    alarmGroupId: string
  ) => void;
  allCameras: Camera[];
}

export function MultiAlarmsMonitor(props: Props) {
  const {
    isFullScreen,
    toggleFullScreen,
    onEscalate,
    activeEscalations,
    onResolveEscalation,
    allCameras,
  } = props;

  const user = useUser();
  const defaultGridConfig = {
    cols: 4,
    rows: 4,
  };
  const [gridConfig, setGridConfig] = useLocalStorage<GridConfig>(
    'scan-grid-config',
    defaultGridConfig
  );
  const scanAlarmsGridRef = useRef<ScanAlarmsGridRef>(null);

  const [isScanPaused, setIsScanPaused] = useState(false);

  const [alarmGroupIdToDetailsMap, setAlarmGroupIdToDetailsMap] = useState<
    Record<string, UnifiedAlarmGroup>
  >({});
  const { invalidateForAlarm } = useInvalidateAIRecommendationCache();

  const camIdToDetailsMap = useMemo<Record<string, Camera>>(() => {
    const map: Record<string, Camera> = {};
    allCameras.forEach((cam) => {
      map[cam.id] = cam;
    });
    return map;
  }, [allCameras]);

  const { send: sendScanWS } = useScanWs();
  const sendMessageInWS = useCallback(
    (data: VisionOutboundPayload) => {
      sendScanWS?.(data);
    },
    [sendScanWS]
  );

  const handleRebalanceEvent = () => {
    toast('A new operator has joined Scan. Your workload will now be balanced');
  };

  const handleNewAlarms = (alarms: UnifiedAlarm[]) => {
    const updatedAlarmGroupIdToDetailsMap: Record<string, UnifiedAlarmGroup> = {
      ...alarmGroupIdToDetailsMap,
    };
    const newAlarmGroupIds: string[] = [];
    alarms.forEach((item) => {
      const cameraDetails = camIdToDetailsMap[item.cameraId];
      const alarm = updatePropertyInUnifiedAlarm(item, cameraDetails);
      const alarmGroupId = alarm.alarmGroupId;
      if (!cameraDetails) {
        // eslint-disable-next-line no-console
        console.error(`Camera ${alarm.cameraId} not found`);
        return;
      }
      if (!updatedAlarmGroupIdToDetailsMap[alarmGroupId]) {
        const camToAlarmsMap = new Map<string, UnifiedAlarm[]>();
        camToAlarmsMap.set(alarm.cameraId, [alarm]);
        const newAlarmGroup: UnifiedAlarmGroup = {
          id: alarm.alarmGroupId,
          timestamp: alarm.timestamp,
          tenantId: cameraDetails.tenantId ?? '',
          location: cameraDetails.location,
          alarms: [alarm],
          status: 'active',
          severity: alarm.severity,
          escalations: [],
          camToAlarmsMap,
        };
        newAlarmGroupIds.push(alarmGroupId);
        updatedAlarmGroupIdToDetailsMap[alarmGroupId] = newAlarmGroup;
      } else {
        updatedAlarmGroupIdToDetailsMap[alarmGroupId].alarms.push(alarm);
        const alarmCameraId = alarm.cameraId;
        const existingCamToAlarmsMap =
          updatedAlarmGroupIdToDetailsMap[alarmGroupId].camToAlarmsMap?.get(
            alarmCameraId
          );
        if (existingCamToAlarmsMap) {
          existingCamToAlarmsMap.push(alarm);
        } else {
          updatedAlarmGroupIdToDetailsMap[alarmGroupId].camToAlarmsMap?.set(
            alarm.cameraId,
            [alarm]
          );
        }
      }
      if (newAlarmGroupIds.length > 0) {
        scanAlarmsGridRef.current?.addAlarmGroups(newAlarmGroupIds);
      }
    });

    setAlarmGroupIdToDetailsMap(updatedAlarmGroupIdToDetailsMap);
  };

  useScanWsMessageHandler(VisionEventType.HISTORY, handleNewAlarms);
  useScanWsMessageHandler(VisionEventType.REBALANCE, handleRebalanceEvent);

  const cleanUpAlarmGroup = (alarmGroupId: string) => {
    const updatedAlarmGroupIdToDetailsMap = { ...alarmGroupIdToDetailsMap };
    delete updatedAlarmGroupIdToDetailsMap[alarmGroupId];
    setAlarmGroupIdToDetailsMap(updatedAlarmGroupIdToDetailsMap);
  };

  const onCellAction = (
    cameraId: string,
    alarmGroupId: string,
    cameraGroupId: string,
    actionType: CamAction,
    comment?: string
  ) => {
    const alarmGroup = alarmGroupIdToDetailsMap[alarmGroupId];
    switch (actionType) {
      case CamAction.SAFE: {
        const message: VisionOutboundPayload = {
          alarm_group_id: alarmGroupId,
          camera_id: cameraId,
          group_id: cameraGroupId,
          tenant_id: alarmGroup.tenantId,
          event_type: VisionOutboundType.SAFE,
          additional_data: {
            username: user.email,
            event_timestamp: Date.now(),
            comment: comment ?? '',
          },
        };
        // TODO: check if it needs to changed to alarmGroupId
        invalidateForAlarm(cameraGroupId);
        sendMessageInWS(message);
        cleanUpAlarmGroup(alarmGroupId);
        break;
      }
      case CamAction.ESCALATE:
      case CamAction.INVESTIGATE: {
        const message: VisionOutboundPayload = {
          alarm_group_id: alarmGroupId,
          camera_id: cameraId,
          group_id: cameraGroupId,
          tenant_id: alarmGroup.tenantId,
          event_type: VisionOutboundType.INVESTIGATE,
          additional_data: {
            username: user.email,
            event_timestamp: Date.now(),
            comment: comment ?? '',
          },
        };
        if (actionType === CamAction.ESCALATE) {
          message.event_type = VisionOutboundType.ESCALATION_OPEN;
          onEscalate(camIdToDetailsMap[cameraId], message);
          // send message in websocket once escalation is created via api
          return;
        }
        sendMessageInWS(message);
        break;
      }
    }
  };

  const handleResolveEscalation = (message: string, alarmGroupId: string) => {
    cleanUpAlarmGroup(alarmGroupId);
    invalidateForAlarm(alarmGroupId);
    const alarmGroup = alarmGroupIdToDetailsMap[alarmGroupId];
    const firstAlarm = alarmGroup.alarms[0];

    onResolveEscalation(message, firstAlarm.cameraGroupId, alarmGroupId);
  };

  const togglePlayPause = () => setIsScanPaused((prev) => !prev);

  const isNoAlarms = Object.keys(alarmGroupIdToDetailsMap).length === 0;

  return (
    <div
      className={clsx(
        'flex min-h-0 min-w-0 flex-grow flex-col',
        isFullScreen ? 'h-[calc(100vh-2rem)]' : 'h-[calc(100%-8px)]',
        // Add responsive handling for small screens
        'sm:min-h-[300px] sm:min-w-[400px]',
        'md:min-h-[400px] md:min-w-[600px]',
        'lg:min-h-[500px] lg:min-w-[800px]'
      )}
    >
      {isScanPaused && isNoAlarms && (
        <div className="m-auto flex h-full w-full items-center justify-center gap-2">
          <Alert type="info" className="m-4">
            Scan is currently paused. Please resume to view active alarms.
          </Alert>
          <Button
            variant="outline"
            onClick={togglePlayPause}
            className="flex gap-2"
          >
            <span>Resume</span>
            <PlayIcon className="h-5 w-5" />
          </Button>
        </div>
      )}
      {!isScanPaused && isNoAlarms && (
        <div className="z-10 flex h-full w-full items-center justify-center">
          <LoadingIndicator text="Fetching Alarms..." />
        </div>
      )}

      <div className="min-h-0 w-full min-w-0 flex-grow">
        <ScanAlarmsGrid
          ref={scanAlarmsGridRef}
          alarmGroupIdToDetailsMap={alarmGroupIdToDetailsMap}
          gridConfig={gridConfig ?? defaultGridConfig}
          onCellAction={onCellAction}
          isScanPaused={isScanPaused}
          activeEscalations={activeEscalations}
          onResolveEscalation={handleResolveEscalation}
        />
      </div>
      <div className="mr-14 flex max-w-[calc(100vw-24px)] flex-wrap items-center gap-2 sm:flex-nowrap">
        <ScanToolbar
          toggleFullScreen={toggleFullScreen}
          isFullScreen={isFullScreen}
          gridConfig={gridConfig ?? defaultGridConfig}
          updateGridConfig={setGridConfig}
          isPaused={isScanPaused}
          togglePlayPause={togglePlayPause}
        />
      </div>
    </div>
  );
}

export default MultiAlarmsMonitor;
