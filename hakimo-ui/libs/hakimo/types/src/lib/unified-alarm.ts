import { Box } from './alarms/alarms';
import { Location } from './location';

export interface Frame {
  id: string;
  url: string;
  timestamp: number;
}

export interface AlarmVideo {
  boxes: Box[];
  url: string;
  startTime: string;
  endTime: string;
}

export interface Media {
  type: string;
  video?: AlarmVideo;
  frames?: Frame[];
}

export interface UnifiedAlarm {
  id: string;
  alarmGroupId: string;
  timestamp: string;
  cameraId: string;
  cameraName: string;
  cameraGroupId: string;
  cameraGroupName: string;
  cameraLiveStreamUrl: string;
  cameraPlayBackUrl: string;
  alarmStartTime: number;
  alarmEndTime: number;
  severity: string;
  media: Media;
  resolvedAlarm: boolean;
  isTalkDownEnabled: boolean;
  isPastEvent?: boolean;
  isDedupEvent?: boolean;
}

export interface Escalation {
  id: string;
  title: string;
  description: string;
  incidentStartTimeUtc: string;
  incidentEndTimeUtc?: string;
  initiatingLocationAlarmId: string;
}

export interface UnifiedAlarmGroup {
  id: string;
  timestamp: string;
  tenantId: string;
  location: Location;
  alarms: UnifiedAlarm[];
  status: string;
  severity: string;
  escalations: Escalation[];
  camToAlarmsMap?: Map<string, UnifiedAlarm[]>;
}

export enum MonitoringType {
  SCAN = 'scan',
  LOCATION_ALARM = 'locationAlarm',
}

export interface AlarmCountResponse {
  type: string;
  operatorId: string;
  count: number;
  monitoringType: MonitoringType;
  severity: string;
}

export interface AlarmListResponse {
  type: string;
  payload: UnifiedAlarmGroup[];
  isSuccess?: boolean;
  error?: string;
}

export interface AlarmGroupResponse {
  type: string;
  payload: UnifiedAlarmGroup;
  isSuccess?: boolean;
  error?: string;
}

export interface NeighbourAlarmResponse {
  type: string;
  payload: {
    startTimestamp: number;
    endTimestamp: number;
    alarms: UnifiedAlarm[];
  };
}

export enum UnifiedAlarmMessageType {
  ALARM_COUNT = 'alarm_count',
  LIST_ALARM = 'list_alarm',
  ALARM = 'alarm',
  FETCH_ALARMS = 'fetchAlarms',
}
