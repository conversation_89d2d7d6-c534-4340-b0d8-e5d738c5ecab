import { Media, UnifiedAlarm } from '@hakimo-ui/hakimo/types';

export interface Person {
  name: string;
  class: number;
  confidence: number;
  box: {
    x1: number;
    y1: number;
    x2: number;
    y2: number;
  };
  person_id: string;
  overlap: number;
}

export enum VisionEventType {
  DETECTION = 'detection',
  HISTORY = 'history',
  REBALANCE = 'rebalance',
  GET_ESCALATIONS = 'get_escalations',
  LLM_EVENT = 'llm_event',
}

export interface VisionEventMetadata {
  persons: Person[];
  frames?: [string, number][];
}

export interface VisionEvent {
  id: string;
  event_id: string;
  camera_id: string;
  tenant_id: string;
  group_id: string;
  camera_group_id: string;
  objects: { person: number };
  timestamp_utc: number;
  p: number;
  camera_name: string;
  metadata: VisionEventMetadata;
  event_type: VisionEventType;
  severity: 'low' | 'high';
  alarm_group_id: string;
  is_past_event?: boolean;
  is_dedup?: boolean;
  event_timestamp_utc: string;
}

export const convertToUnifiedAlarm = (
  event: VisionEvent
): UnifiedAlarm | null => {
  const frames = event.metadata.frames?.map(([url, timestamp]) => ({
    id: event.id,
    url,
    timestamp,
  }));

  if (!frames) {
    return null;
  }

  const frameTimestamp = frames.map((frame) => frame.timestamp);
  frameTimestamp.sort();
  const eventStartTime = frameTimestamp[0];
  const eventEndTime = frameTimestamp[frameTimestamp.length - 1];

  const media: Media = {
    type: 'frames',
    frames: frames,
  };

  return {
    id: event.event_id,
    alarmGroupId: event.alarm_group_id,
    timestamp: event.event_timestamp_utc,
    cameraId: event.camera_id,
    cameraName: event.camera_name,
    cameraGroupId: event.camera_group_id,
    cameraGroupName: '', // Not available in EventDetails
    cameraLiveStreamUrl: '', // Not available in EventDetails
    cameraPlayBackUrl: '', // Not available in EventDetails
    alarmStartTime: eventStartTime,
    alarmEndTime: eventEndTime,
    severity: event.severity,
    media: media,
    resolvedAlarm: false,
    isTalkDownEnabled: false, // Not available in EventDetails
    isPastEvent: event.is_past_event,
    isDedupEvent: event.is_dedup,
  };
};
