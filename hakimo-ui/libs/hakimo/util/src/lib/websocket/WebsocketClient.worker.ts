/* eslint-disable no-console */
/* eslint-disable max-lines */
import {
  RetryConfig,
  WebSocketClientOptions,
  WsConnectionState,
  WsConnectionStatus,
  WSDataType,
} from '@hakimo-ui/hakimo/types';
import { expose } from 'comlink';
import { ExponentialBackoff } from './backoff';
import { MessageBatcher } from './batcher';
import { convertToUnifiedAlarm, VisionEventType } from './util';

interface ParsedMessage {
  [key: string]: unknown;
}

interface MessageHandlerPayload {
  type: string;
  data: ParsedMessage | ParsedMessage[];
}

class WebSocketClient {
  private ws?: WebSocket;
  private url?: string;
  private token?: string;
  private backoff: ExponentialBackoff = new ExponentialBackoff();
  private batcher?: MessageBatcher<ParsedMessage>;
  private retryAttempt = 0;
  private isIntentional = false;
  private autoReconnect = true;
  private handlers = {
    message: undefined as ((p: MessageHandlerPayload) => void) | undefined,
    state: undefined as ((s: WsConnectionState) => void) | undefined,
  };
  private retryTimeoutId?: NodeJS.Timeout;
  private isDuplicateSession = false;
  private retryDelay = 0;
  private dataTypeKey = 'type';
  private pendingMessageQueue: string[] = [];

  setInitOptions(options: WebSocketClientOptions = {}) {
    this.backoff = new ExponentialBackoff(options.retryConfig);
    this.autoReconnect = options.autoReconnect ?? true;
    this.dataTypeKey = options.dataTypeKey || 'type';

    if (options.batchDataTypes && options.batchDataTypes.length) {
      this.batcher = new MessageBatcher<ParsedMessage>(
        options.batchDataTypes,
        options.flushInterval
      );
      this.batcher.onBatch((type, data) =>
        this.handlers.message?.({ type, data })
      );
    }
  }

  public setMessageHandler(cb: (p: MessageHandlerPayload) => void): void {
    this.handlers.message = cb;
  }

  public setConnectionStateHandler(cb: (s: WsConnectionState) => void): void {
    this.handlers.state = cb;
  }

  public async connect(url: string, token?: string): Promise<void> {
    this.isIntentional = false;
    this.url = url;
    this.token = token;
    this.retryAttempt = 0;
    await this.openConnection();
  }

  private async openConnection(): Promise<void> {
    if (!this.url) throw new Error('URL not set');
    this.clearRetry();
    this.ws = new WebSocket(this.url, this.token ? [this.token] : []);
    if (this.retryAttempt < 1) {
      this.notifyState(WsConnectionStatus.PENDING);
    }

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        this.notifyState(WsConnectionStatus.ERROR);
        reject(new Error('Connection timeout'));
      }, 300000); // 5 min.

      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      this.ws!.onopen = () => {
        clearTimeout(timer);

        // reset retry count & notify
        this.isDuplicateSession = false;
        this.retryAttempt = 0;
        this.notifyState(WsConnectionStatus.CONNECTED);

        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        this.ws!.onclose = () => this.handleClose();
        resolve();

        // check pending message queue and send it
        if (this.pendingMessageQueue.length > 0) {
          this.pendingMessageQueue.forEach((msg) => this.send(msg));
          this.pendingMessageQueue = [];
        }
      };
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      this.ws!.onmessage = (e) => this.handleMessage(e);

      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      this.ws!.onclose = () => {
        clearTimeout(timer);
        this.handleClose();
      };

      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      this.ws!.onerror = (err) => {
        clearTimeout(timer);
        reject(err);
      };
    });
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const msg = JSON.parse(event.data);
      const type = msg[this.dataTypeKey] as string;
      if (type === WSDataType.DUPLICATE_CONNECTION) {
        this.isDuplicateSession = true;
        return;
      }

      if (this.batcher && this.batcher.isTypeEligibleForBatch(type)) {
        let updatedMsg = msg;
        if (type === VisionEventType.HISTORY) {
          updatedMsg = convertToUnifiedAlarm(msg);
          if (!updatedMsg) {
            console.error('Failed to convert history event to unified alarm');
            return;
          }
        }
        this.batcher.add(type, updatedMsg);
      } else {
        this.handlers.message?.({ type, data: msg });
      }
    } catch (err) {
      console.error('Message parse error', err);
    }
  }

  private handleClose(): void {
    this.ws = undefined;
    if (!this.isIntentional && this.autoReconnect) {
      this.scheduleRetry();
    }
  }

  private scheduleRetry(): void {
    this.retryAttempt = this.retryAttempt + 1;

    if (!this.backoff.canRetry(this.retryAttempt) || this.isDuplicateSession) {
      this.notifyState(WsConnectionStatus.CLOSED);
      return;
    }
    const delay = this.backoff.getDelay(this.retryAttempt);
    this.retryDelay = Math.ceil(delay / 1000);
    const newState = {
      status: WsConnectionStatus.RECONNECTING,
      retryAttempt: this.retryAttempt,
      isRetrying: true,
      nextRetryIn: this.retryDelay,
      isDuplicateSession: this.isDuplicateSession,
    };
    this.handlers.state?.(newState);

    const retryIntervalId = setInterval(() => {
      this.retryDelay -= 1;
      this.handlers.state?.({
        ...newState,
        nextRetryIn: this.retryDelay,
      });
      if (this.retryDelay <= 0) {
        clearInterval(retryIntervalId);
      }
    }, 1000);

    this.retryTimeoutId = setTimeout(() => {
      clearInterval(retryIntervalId);
      this.openConnection();
      // .catch(() => this.scheduleRetry());
    }, delay);
  }

  private notifyState(status: WsConnectionStatus): void {
    this.handlers.state?.({
      status,
      isDuplicateSession: this.isDuplicateSession,
      retryAttempt: this.retryAttempt,
      isRetrying: Boolean(this.retryTimeoutId),
    });
  }

  public send(msg: string): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(msg);
    } else {
      console.error(
        'Websocket not open, adding message to pending queue. These messages will be sent when the connection is re-established.'
      );
      this.pendingMessageQueue.push(msg);
    }
  }

  public disconnect(): void {
    this.isIntentional = true;
    this.clearRetry();
    this.ws?.close(1000, 'Client disconnect');
    if (this.batcher) this.batcher.dispose();
    this.ws = undefined;
    this.notifyState(WsConnectionStatus.CLOSED);
  }

  private clearRetry(): void {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
      this.retryTimeoutId = undefined;
    }
  }
}

const wsClient = new WebSocketClient();
expose(wsClient);
export type {
  MessageHandlerPayload,
  RetryConfig,
  WebSocketClient,
  WebSocketClientOptions
};

